<?php
/**
 * Core functionality class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Core class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Core {

    /**
     * Instance of this class
     *
     * @var MAP_Core
     * @since 1.0.0
     */
    private static $instance = null;

    /**
     * Settings instance
     *
     * @var MAP_Settings
     * @since 1.0.0
     */
    public $settings;

    /**
     * Text to Speech instance
     *
     * @var MAP_Text_To_Speech
     * @since 1.0.0
     */
    public $text_to_speech;

    /**
     * Get instance
     *
     * @return MAP_Core
     * @since 1.0.0
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    private function __construct() {
        $this->init();
    }

    /**
     * Initialize core functionality
     *
     * @since 1.0.0
     */
    private function init() {
        // Initialize settings
        $this->settings = new MAP_Settings();
        
        // Initialize text to speech
        $this->text_to_speech = new MAP_Text_To_Speech();
        
        // Add hooks
        $this->add_hooks();
    }

    /**
     * Add WordPress hooks
     *
     * @since 1.0.0
     */
    private function add_hooks() {
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Add plugin action links
        add_filter('plugin_action_links_' . MAP_PLUGIN_BASENAME, array($this, 'add_action_links'));
        
        // Add plugin meta links
        add_filter('plugin_row_meta', array($this, 'add_meta_links'), 10, 2);
        
        // AJAX handlers
        add_action('wp_ajax_map_save_settings', array($this, 'ajax_save_settings'));
        add_action('wp_ajax_nopriv_map_get_text_content', array($this, 'ajax_get_text_content'));
        add_action('wp_ajax_map_get_text_content', array($this, 'ajax_get_text_content'));
    }

    /**
     * Enqueue frontend assets
     *
     * @since 1.0.0
     */
    public function enqueue_frontend_assets() {
        // Get settings with fallback - ensure settings object exists
        try {
            if (!$this->settings) {
                $this->settings = new MAP_Settings();
            }
            $settings = $this->settings->get_settings();
        } catch (Exception $e) {
            // Fallback to enable by default if settings fail
            $settings = array('text_to_speech_enabled' => true);
        }

        // Ensure settings is an array
        if (!is_array($settings)) {
            $settings = array('text_to_speech_enabled' => true);
        }

        // Set default if key is missing
        if (!isset($settings['text_to_speech_enabled'])) {
            $settings['text_to_speech_enabled'] = true;
        }

        // Only load if text-to-speech is enabled
        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        // Enqueue CSS
        wp_enqueue_style(
            'map-frontend',
            MAP_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            MAP_VERSION
        );

        // Add resource hints for OpenDyslexic font CDN
        add_action('wp_head', array($this, 'add_dyslexic_font_preconnect'), 1);
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'map-frontend',
            MAP_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            MAP_VERSION,
            true
        );
        
        // Ensure we have all required settings with defaults
        $settings = wp_parse_args($settings, array(
            'speech_rate' => 1.0,
            'speech_pitch' => 1.0,
            'speech_volume' => 1.0,
            'widget_position' => 'bottom-right'
        ));

        // Localize script
        wp_localize_script('map-frontend', 'mapAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('map_nonce'),
            'settings' => array(
                'speech_rate' => $settings['speech_rate'],
                'speech_pitch' => $settings['speech_pitch'],
                'speech_volume' => $settings['speech_volume'],
                'widget_position' => $settings['widget_position']
            ),
            'strings' => array(
                'play' => __('Play', MAP_TEXT_DOMAIN),
                'pause' => __('Pause', MAP_TEXT_DOMAIN),
                'stop' => __('Stop', MAP_TEXT_DOMAIN),
                'loading' => __('Loading...', MAP_TEXT_DOMAIN),
                'error' => __('Speech synthesis not supported in this browser.', MAP_TEXT_DOMAIN)
            )
        ));
    }

    /**
     * Add resource hints for OpenDyslexic font CDN
     *
     * @since 1.0.0
     */
    public function add_dyslexic_font_preconnect() {
        // Add preconnect for CDN domain to improve font loading performance
        echo '<link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>' . "\n";
        echo '<link rel="dns-prefetch" href="https://cdn.jsdelivr.net">' . "\n";

        // Add preload for the specific font CSS file (optional - only if we want to preload)
        // This is commented out by default to avoid loading the font unless needed
        // echo '<link rel="preload" href="https://cdn.jsdelivr.net/npm/open-dyslexic@1.0.0/open-dyslexic.min.css" as="style" crossorigin>' . "\n";
    }

    /**
     * Enqueue admin assets
     *
     * @param string $hook
     * @since 1.0.0
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin settings page
        if ('settings_page_my-accessibility-plugin' !== $hook) {
            return;
        }
        
        // Enqueue CSS
        wp_enqueue_style(
            'map-admin',
            MAP_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            MAP_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'map-admin',
            MAP_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            MAP_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('map-admin', 'mapAdmin', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('map_admin_nonce'),
            'strings' => array(
                'saved' => __('Settings saved successfully!', MAP_TEXT_DOMAIN),
                'error' => __('Error saving settings. Please try again.', MAP_TEXT_DOMAIN)
            )
        ));
    }

    /**
     * Add plugin action links
     *
     * @param array $links
     * @return array
     * @since 1.0.0
     */
    public function add_action_links($links) {
        $settings_link = sprintf(
            '<a href="%s">%s</a>',
            admin_url('options-general.php?page=my-accessibility-plugin'),
            __('Settings', MAP_TEXT_DOMAIN)
        );
        
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Add plugin meta links
     *
     * @param array $links
     * @param string $file
     * @return array
     * @since 1.0.0
     */
    public function add_meta_links($links, $file) {
        if (MAP_PLUGIN_BASENAME === $file) {
            $links[] = sprintf(
                '<a href="%s" target="_blank">%s</a>',
                'https://yourwebsite.com/documentation',
                __('Documentation', MAP_TEXT_DOMAIN)
            );
            
            $links[] = sprintf(
                '<a href="%s" target="_blank">%s</a>',
                'https://yourwebsite.com/support',
                __('Support', MAP_TEXT_DOMAIN)
            );
        }
        
        return $links;
    }

    /**
     * AJAX handler for saving settings
     *
     * @since 1.0.0
     */
    public function ajax_save_settings() {
        // Verify nonce
        if (!MAP_Security::verify_nonce($_POST['nonce'], 'map_admin_nonce')) {
            MAP_Security::log_security_event('invalid_nonce', array('action' => 'save_settings'));
            wp_die(__('Security check failed.', MAP_TEXT_DOMAIN));
        }

        // Check user capabilities
        if (!MAP_Security::check_capability('manage_options')) {
            MAP_Security::log_security_event('insufficient_permissions', array('action' => 'save_settings'));
            wp_die(__('You do not have permission to perform this action.', MAP_TEXT_DOMAIN));
        }

        // Check origin
        if (!MAP_Security::check_origin()) {
            MAP_Security::log_security_event('invalid_origin', array('action' => 'save_settings'));
            wp_die(__('Invalid request origin.', MAP_TEXT_DOMAIN));
        }

        // Validate and sanitize settings
        $settings = $this->settings->validate_settings($_POST);

        // Update settings
        $this->settings->update_settings($settings);

        wp_send_json_success(array(
            'message' => __('Settings saved successfully!', MAP_TEXT_DOMAIN)
        ));
    }

    /**
     * AJAX handler for getting text content
     *
     * @since 1.0.0
     */
    public function ajax_get_text_content() {
        // Verify nonce
        if (!MAP_Security::verify_nonce($_POST['nonce'], 'map_nonce')) {
            MAP_Security::log_security_event('invalid_nonce', array('action' => 'get_text_content'));
            wp_send_json_error(__('Security check failed.', MAP_TEXT_DOMAIN));
        }

        // Check origin
        if (!MAP_Security::check_origin()) {
            MAP_Security::log_security_event('invalid_origin', array('action' => 'get_text_content'));
            wp_send_json_error(__('Invalid request origin.', MAP_TEXT_DOMAIN));
        }

        // Sanitize text using security class
        $text = MAP_Security::sanitize_speech_text($_POST['text']);

        // Process text for speech
        $processed_text = $this->text_to_speech->process_text($text);

        wp_send_json_success(array(
            'text' => $processed_text
        ));
    }

    /**
     * Get plugin settings
     *
     * @return array
     * @since 1.0.0
     */
    public function get_settings() {
        return $this->settings->get_settings();
    }
}
