<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007cba;
            color: white;
        }
        button:hover {
            background-color: #005a87;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Translation System Test</h1>
    
    <div class="test-section info">
        <h3>Test Instructions</h3>
        <p>This test will verify that the new JSON-based translation system works correctly.</p>
        <p>Click the buttons below to test different aspects of the translation system.</p>
    </div>

    <div class="test-section">
        <h3>Translation Loading Tests</h3>
        <button onclick="testEnglishLoad()">Test English Loading</button>
        <button onclick="testFrenchLoad()">Test French Loading</button>
        <button onclick="testInvalidLanguage()">Test Invalid Language</button>
    </div>

    <div class="test-section">
        <h3>Translation Retrieval Tests</h3>
        <button onclick="testTranslationKeys()">Test Translation Keys</button>
        <button onclick="testFallbackBehavior()">Test Fallback Behavior</button>
    </div>

    <div id="results"></div>

    <script>
        // Mock mapAjax object for testing
        window.mapAjax = {
            plugin_url: './'
        };

        // Simple translation loader class for testing
        class TranslationTester {
            constructor() {
                this.translations = {};
                this.translationsLoaded = false;
                this.translationLoadPromises = {};
                this.currentLanguage = 'en';
            }

            async loadLanguage(langCode) {
                if (this.translationLoadPromises[langCode]) {
                    return this.translationLoadPromises[langCode];
                }

                if (this.translations[langCode]) {
                    return Promise.resolve();
                }

                this.translationLoadPromises[langCode] = new Promise(async (resolve, reject) => {
                    try {
                        const pluginUrl = mapAjax.plugin_url || './';
                        const response = await fetch(`${pluginUrl}languages/${langCode}.json`);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        
                        const translations = await response.json();
                        
                        // Flatten the nested JSON structure
                        const flatTranslations = {};
                        Object.values(translations).forEach(category => {
                            Object.assign(flatTranslations, category);
                        });
                        
                        this.translations[langCode] = flatTranslations;
                        resolve();
                    } catch (error) {
                        console.error(`Failed to load translations for ${langCode}:`, error);
                        reject(error);
                    } finally {
                        delete this.translationLoadPromises[langCode];
                    }
                });

                return this.translationLoadPromises[langCode];
            }

            translate(key) {
                if (!this.translationsLoaded) {
                    return key;
                }
                
                if (this.translations[this.currentLanguage] && this.translations[this.currentLanguage][key]) {
                    return this.translations[this.currentLanguage][key];
                }
                
                if (this.translations.en && this.translations.en[key]) {
                    return this.translations.en[key];
                }
                
                return key;
            }
        }

        const tester = new TranslationTester();

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testEnglishLoad() {
            try {
                addResult('Testing English translation loading...', 'info');
                await tester.loadLanguage('en');
                tester.translationsLoaded = true;
                tester.currentLanguage = 'en';
                
                const testKey = 'Accessibility Tools';
                const translation = tester.translate(testKey);
                
                if (translation === testKey) {
                    addResult(`✓ English loaded successfully. Translation for "${testKey}": "${translation}"`, 'success');
                } else {
                    addResult(`✗ English loading failed. Expected "${testKey}", got "${translation}"`, 'error');
                }
            } catch (error) {
                addResult(`✗ English loading failed: ${error.message}`, 'error');
            }
        }

        async function testFrenchLoad() {
            try {
                addResult('Testing French translation loading...', 'info');
                await tester.loadLanguage('fr');
                tester.translationsLoaded = true;
                tester.currentLanguage = 'fr';
                
                const testKey = 'Accessibility Tools';
                const translation = tester.translate(testKey);
                
                if (translation === 'Outils d\'Accessibilité') {
                    addResult(`✓ French loaded successfully. Translation for "${testKey}": "${translation}"`, 'success');
                } else {
                    addResult(`✗ French loading failed. Expected "Outils d'Accessibilité", got "${translation}"`, 'error');
                }
            } catch (error) {
                addResult(`✗ French loading failed: ${error.message}`, 'error');
            }
        }

        async function testInvalidLanguage() {
            try {
                addResult('Testing invalid language handling...', 'info');
                await tester.loadLanguage('invalid');
                addResult('✗ Invalid language should have failed but didn\'t', 'error');
            } catch (error) {
                addResult(`✓ Invalid language correctly failed: ${error.message}`, 'success');
            }
        }

        async function testTranslationKeys() {
            try {
                addResult('Testing various translation keys...', 'info');
                
                // Load English first
                await tester.loadLanguage('en');
                tester.translationsLoaded = true;
                tester.currentLanguage = 'en';
                
                const testKeys = [
                    'Text Options',
                    'Navigation Options', 
                    'Contrast & Colors',
                    'Preferences'
                ];
                
                let passed = 0;
                for (const key of testKeys) {
                    const translation = tester.translate(key);
                    if (translation && translation !== key) {
                        passed++;
                        addResult(`✓ Key "${key}": "${translation}"`, 'success');
                    } else {
                        addResult(`✗ Key "${key}" not found or empty`, 'error');
                    }
                }
                
                addResult(`Translation keys test: ${passed}/${testKeys.length} passed`, passed === testKeys.length ? 'success' : 'error');
            } catch (error) {
                addResult(`✗ Translation keys test failed: ${error.message}`, 'error');
            }
        }

        async function testFallbackBehavior() {
            try {
                addResult('Testing fallback behavior...', 'info');
                
                // Load both languages
                await tester.loadLanguage('en');
                await tester.loadLanguage('fr');
                tester.translationsLoaded = true;
                
                // Test fallback to English
                tester.currentLanguage = 'fr';
                const nonExistentKey = 'NonExistentKey123';
                const fallback = tester.translate(nonExistentKey);
                
                if (fallback === nonExistentKey) {
                    addResult(`✓ Fallback working correctly. Non-existent key returns: "${fallback}"`, 'success');
                } else {
                    addResult(`✗ Fallback not working. Expected "${nonExistentKey}", got "${fallback}"`, 'error');
                }
                
                // Test existing key in French
                const existingKey = 'Accessibility Tools';
                const frenchTranslation = tester.translate(existingKey);
                if (frenchTranslation === 'Outils d\'Accessibilité') {
                    addResult(`✓ French translation working: "${existingKey}" → "${frenchTranslation}"`, 'success');
                } else {
                    addResult(`✗ French translation failed: "${existingKey}" → "${frenchTranslation}"`, 'error');
                }
                
            } catch (error) {
                addResult(`✗ Fallback test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            addResult('Translation system test page loaded. Click buttons above to run tests.', 'info');
        });
    </script>
</body>
</html>
